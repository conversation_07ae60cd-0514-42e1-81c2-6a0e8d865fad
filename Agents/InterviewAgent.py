from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_core.runnables import RunnableLambda
from pydantic import BaseModel, Field
from typing import List, Optional

from LLM.LLMManager import sys_llm_manager
from Utils.CommonUtils import remove_think_tags, fix_json_quotes
from Utils.logs.LoggingConfig import logger



# 防止ai处理内循环时出现段落循环
# 子级
class EvaluationNode(BaseModel):
    id: int = Field(default=0, description="节点ID(从1开始自增)")
    parentId: Optional[int] = Field(default=None, description="父级ID")
    title: str = Field(default="", description="综合评价")
    score: int = Field(default=0, description="得分，范围0-17或0-16")
# 父级
class InterviewEvaluationItem(BaseModel):
    """面试评价子项"""
    id: int = Field(default=0, description="节点ID(从1开始自增)")
    title: str = Field(description="综合评价")
    score: int = Field(description="得分")
    children: Optional[List[EvaluationNode]] = Field(default=None, description="单项评价")
# 外层综合评价
class InterviewSummaryModel(BaseModel):
    summary: str = Field(default=None, description="根据面试者的回答，给出面试综合评价")
    score: int = Field(default=0, description="得分范围为0-100，0分表示面试评价较差，100分表示面试评价较好")
    evaluation_info: Optional[InterviewEvaluationItem] = Field(default=None, description="面试评价对象")

# 精简版模型：仅包含 summary 与 score，用于实时面试场景
class SimpleInterviewEvaluation(BaseModel):
    summary: str = Field(..., description="评语")
    score: int = Field(..., description="总分(0-100)")


class InterviewAgent:
    TEMPERATURE = 0.5

    @staticmethod
    def _get_default_evaluation(message: str = "输入内容不足，无法进行详细评价。请提供完整的面试对话内容。") -> dict:
        """获取默认评价结果"""
        return {
            "summary": message,
            "score": 0,
            "evaluation_info": """{
              "id": 1,
              "parentId": null,
              "title": "面试综合评价",
              "score": 0,
              "children": [
                {
                  "id": 2,
                  "parentId": 1,
                  "title": "专业能力",
                  "score": 0,
                  "children": []
                },
                {
                  "id": 3,
                  "parentId": 1,
                  "title": "沟通能力",
                  "score": 0,
                  "children": []
                },
                {
                  "id": 4,
                  "parentId": 1,
                  "title": "学习能力",
                  "score": 0,
                  "children": []
                },
                {
                  "id": 5,
                  "parentId": 1,
                  "title": "问题解决能力",
                  "score": 0,
                  "children": []
                },
                {
                  "id": 6,
                  "parentId": 1,
                  "title": "抗压能力",
                  "score": 0,
                  "children": []
                },
                {
                  "id": 7,
                  "parentId": 1,
                  "title": "规划与稳定性",
                  "score": 0,
                  "children": []
                }
              ]
            }"""
        }

    @staticmethod
    def generate_evaluation(content: str) -> dict:
        """生成面试评价"""
        logger.info(f"开始生成面试评价，输入内容长度: {len(content)}")
        
        try:
            # 生成面试评价
            logger.debug("初始化LLM模型")
            llm = sys_llm_manager.get_generate_use_llm_helper().get_llm_chat_object(InterviewAgent.TEMPERATURE)

            logger.debug("初始化PydanticOutputParser")
            parser = PydanticOutputParser(pydantic_object=InterviewSummaryModel)

            logger.debug("构建提示模板")
            prompt = SystemMessagePromptTemplate.from_template(
                        """你是一个经验丰富的面试官，擅长根据应聘者在面试过程中的对话内容进行分析并给出评价。
                        评判规则：
                            - 你必须以面试官的第一人称对接下来的对话内容进行评分，不允许超过单项评分的满分。
                            - 根据对话内容对该应聘者的几个维度的进行单项评分，不允许超过单项评分的满分。
                            - 根据评分维度结合对话内容进行分析，如果内容中为能出现相关维度信息和含义则判为 0 分。
                            - 结合评分维度和对话内容分析这两项，给出具有参考意义的综合评价。
                        评分维度：
                            - 专业能力：(满分：17分) - 评估应聘者的专业技能和知识水平。
                            - 沟通能力：(满分：17分) - 评估应聘者的表达能力和沟通技巧。
                            - 学习能力：(满分：17分) - 评估应聘者的学习意愿和适应能力。
                            - 问题解决能力：(满分：17分) - 评估应聘者的分析问题和解决问题的能力。
                            - 抗压能力：(满分：16分) - 评估应聘者在压力下的表现和应对能力。
                            - 规划与稳定性：(满分：16分) - 评估应聘者的职业规划和稳定性。
                        格式示例：
                            {{
                              "summary": "...",
                              "score": 分值,
                              "evaluation_info": {{
                                "id": 1,
                                "title": "面试综合评价",
                                "score": 分值,
                                "children": [
                                  {{
                                    "id": 2,
                                    "parentId": 1,
                                    "title": "专业能力",
                                    "score": 分值
                                  }},
                                  ...
                                ]
                              }}
                            }}
                        输出格式：{format_instructions}
                        /no_think
                        """
                    )
            input_template = HumanMessagePromptTemplate.from_template(
                "面试对话记录：{input}"
            )
            logger.info(f"组装提示词信息")
            prompt_template = ChatPromptTemplate.from_messages([
                prompt,
                input_template
            ]).partial(format_instructions=parser.get_format_instructions())

            logger.debug("构建处理链")
            chain = (prompt_template
                        | llm
                        | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                        | RunnableLambda(remove_think_tags)
                        | RunnableLambda(fix_json_quotes)
                        | parser )

            logger.info("开始调用LLM生成评价")
            res = chain.invoke({"input": content})
            
            logger.info(f"LLM生成评价完成，结果: {res}")
            result = res.model_dump()
            logger.info(f"面试评价生成成功，总分: {result.get('score', 0)}, 评价长度: {len(result.get('summary', ''))}")
            
            return result
            
        except Exception as e:
            logger.error(f"生成面试评价时发生错误: {str(e)}")
            # 返回默认评价结果
            logger.info("返回默认评价结果")
            return InterviewAgent._get_default_evaluation("由于输入内容不完整或格式问题，无法生成详细评价。请提供完整的面试对话内容。")

    @staticmethod
    def generate_evaluation_in_interview(content: str, history: str = "") -> dict:
        """根据面试实时对话内容生成评价。

        参数说明：
            content : 当前面试对话（或 Java 侧拼接的完整内容）
            history : 历史对话，可为空
        返回：
            纯文本评语，包含各维度得分与综合评价
        """

        # 1. 初始化 LLM & 解析器（仅 summary、score）
        llm = sys_llm_manager.get_generate_use_llm_helper().get_llm_chat_object(InterviewAgent.TEMPERATURE)

        parser = PydanticOutputParser(pydantic_object=SimpleInterviewEvaluation)

        # 2. 组装提示词
        if history:
            system_template = (
                """你是一名经验丰富、保持客观中立的面试评价专家，只对应聘者进行评分与评价。\n"
                "面试题信息及其标准答案已包含在文本中，请结合标准答案与应聘者的回答进行评分。\n"
                "请输出 JSON，字段如下：summary (字符串评语)，score (0-100 的整数)。\n"
                "以下是面试历史：\n{history}\n\n/no_think"""
            )
            prompt = ChatPromptTemplate.from_messages([
                ("system", system_template),
                ("human", "[面试内容]\n{input}")
            ]).partial(history=history, format_instructions=parser.get_format_instructions())
        else:
            system_template = (
                """你是一名经验丰富、保持客观中立的面试评价专家，只对应聘者进行评分与评价。\n"
                "面试题信息及其标准答案已包含在文本中，请结合标准答案与应聘者的回答进行评分。\n"
                "请输出 JSON，字段如下：summary (字符串评语)，score (0-100 的整数)。\n/no_think"""
            )
            prompt = ChatPromptTemplate.from_messages([
                ("system", system_template),
                ("human", "[面试内容]\n{input}")
            ]).partial(format_instructions=parser.get_format_instructions())

        # 3. 构建处理链
        chain = (
            prompt
            | llm
            | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
            | RunnableLambda(remove_think_tags)
            | RunnableLambda(fix_json_quotes)
            | parser
        )

        # 4. 调用 LLM
        result = chain.invoke({"input": content}).model_dump()

        return result
