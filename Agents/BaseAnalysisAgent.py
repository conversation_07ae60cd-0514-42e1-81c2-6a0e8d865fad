import asyncio
from abc import abstractmethod, ABC
from typing import List, Optional, TypeVar, Generic

from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain_core.documents import Document
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import Run<PERSON>bleLambda
from langchain_core.runnables.history import RunnableWithMessageHistory
from pydantic import BaseModel

from LLM.LLMManager import sys_llm_manager
from Models.peewee.OrmModel import TaskState, KBAnalysisTask
from Services.ContentLoader.LoaderFactory import LoaderFactory
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorRetrivalService
from Utils.CommonUtils import from_json_str, remove_think_tags, fix_json_quotes
from Utils.logs.LoggingConfig import logger

T = TypeVar('T', bound=BaseModel)


class AnalysisSqlRequest(BaseModel, Generic[T]):
    contract_content: Optional[List[T]] = None
    contract_chunks: Optional[List[Document]] = None


# 合同审查agent，一个文件一个任务
class BaseAnalysisAgent(ABC):
    TEMPERATURE = 0.3
    TASK_COUNT = 1

    def __init__(self):
        self.__conversation = self.__set_llm()
        type = self.get_data_model()
        self.sql_request = AnalysisSqlRequest[type](contract_content=[], contract_chunks=[])
        self.deal_chunk_count = 0

    @abstractmethod
    def get_data_model(self) -> type[BaseModel]:
        pass

    @abstractmethod
    def get_kb_ids(self) -> list[int]:
        pass

    @abstractmethod
    def get_prompt(self) -> str:
        pass

    @abstractmethod
    def filter_data(self, data: BaseModel) -> BaseModel:
        pass

    @abstractmethod
    def save_extra(self, data: List[BaseModel]) -> str:
        pass

    @staticmethod
    def deal_result(results: List[BaseModel]) -> BaseModel:
        pass

    def get_temperature(self) -> float:
        return self.TEMPERATURE

    # 根据LLM的名字设置LLM相关信息
    def __set_llm(self) -> RunnableWithMessageHistory | None:
        # 使用非思考模型
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()

        if llm_helper is None:
            return None

        llm = llm_helper.get_llm_chat_object(self.get_temperature())

        parser = PydanticOutputParser(pydantic_object=self.get_data_model())

        system_template = SystemMessagePromptTemplate.from_template(
            """请严格按照以下要求执行任务：
            
            {prompt}
            
             请严格按照以下格式输出：
             1、引用的原文内容使用单引号。
             2、输出时严格检查文本是否符合json格式。
             
            {format_instructions}
            
            /no_think
           """
        )

        human_template = HumanMessagePromptTemplate.from_template(
            "待分析文件内容：\n{content}\n\n"
            "知识库相关内容：\n{knowledge}"
        )

        prompt_template = ChatPromptTemplate.from_messages([
            system_template,
            human_template
        ]).partial(format_instructions=parser.get_format_instructions(), prompt=self.get_prompt())

        conversation = (prompt_template | llm
                        | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                        | RunnableLambda(remove_think_tags)
                        | RunnableLambda(fix_json_quotes)
                        | parser)

        return conversation

    def query_kb_knowledge(self, question: str):
        knowledge = TrunkVectorRetrivalService.hybrid_search(query=question, kb_ids=self.get_kb_ids())
        return knowledge

    async def run(self, work_id: int, file_path: str):
        from Services.SqlServer.KBAnalysisTaskServer import KBAnalysisTaskServer
        # 更新状态
        KBAnalysisTaskServer.update_state(work_id, TaskState.PROCESSING)
        # 查询是否已经生成块
        work_info = KBAnalysisTaskServer.get_by_id(work_id)

        temp_chunk = []
        if work_info.result and "contract_content" in work_info.result:
            self.sql_request = from_json_str(work_info.result, type(self.sql_request))
            temp_chunk = self.sql_request.contract_chunks
            self.deal_chunk_count = work_info.deal_chunk_count
        else:
            # 获取加载器
            loader = LoaderFactory.get_file_loader(file_path, 500)
            for chunks in loader.load(file_path):
                if not chunks:
                    continue
                temp_chunk.append(chunks)
            self.sql_request.contract_chunks = temp_chunk
            # 更新切块数量
            KBAnalysisTaskServer.update(work_id, {KBAnalysisTask.chunk_count.name: len(temp_chunk),
                                                  KBAnalysisTask.result.name: self.sql_request.model_dump_json()})

        task_list = []
        for chunk in temp_chunk[self.deal_chunk_count:]:
            task_list.append(self.__task(chunk.page_content))
            if len(task_list) == self.TASK_COUNT:
                await self._process_batch_tasks(work_id, task_list)
        # 处理剩余任务
        if task_list:
            await self._process_batch_tasks(work_id, task_list)
        # 存储结果
        KBAnalysisTaskServer.update_state(work_id, TaskState.COMPLETED)

    async def _process_batch_tasks(self, work_id: int, tasks: list):
        """处理批量任务的通用方法"""
        from Services.SqlServer.KBAnalysisTaskServer import KBAnalysisTaskServer
        results = []
        logger.error(tasks)
        batch_results = await asyncio.gather(*tasks)
        for result in batch_results:
            if result:
                results.append(result)
        self.deal_chunk_count += len(batch_results)

        try:
            if results:
                self.sql_request.contract_content.extend(results)
                extra = self.save_extra(self.sql_request.contract_content)
                KBAnalysisTaskServer.update(work_id, {KBAnalysisTask.deal_chunk_count.name: self.deal_chunk_count,
                                                      KBAnalysisTask.result.name: self.sql_request.model_dump_json(),
                                                      KBAnalysisTask.extra.name: extra})
            else:
                KBAnalysisTaskServer.update(work_id, {KBAnalysisTask.deal_chunk_count.name: self.deal_chunk_count})
        except Exception as e:
            logger.error(f"Error in _process_batch_tasks: {str(e)}")
            # 如果出错那条数据就不要了
            KBAnalysisTaskServer.update(work_id, {KBAnalysisTask.deal_chunk_count.name: self.deal_chunk_count})
        tasks.clear()

    async def __task(self, trunk: str) -> BaseModel | None:
        # 搜索
        knowledge = self.query_kb_knowledge(trunk)

        knowledge_result = "\n".join([doc.page_content for doc in knowledge])
        # 添加重试机制
        max_retries = 2
        result = None
        for attempt in range(max_retries):
            try:
                result = await self.__conversation.ainvoke({
                    "content": trunk,
                    "knowledge": knowledge_result,
                })
                return self.filter_data(result)
            except Exception as e:
                logger.error(f"Error in __task: {str(result)}{str(e)}")
                if attempt < max_retries - 1:
                    logger.error(f"Retrying task... (Attempt {attempt + 1}/{max_retries})")
        return result
