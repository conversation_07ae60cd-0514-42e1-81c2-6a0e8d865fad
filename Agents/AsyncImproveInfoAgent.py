import asyncio
from Agents.ImproveInfoAgent import ImproveInfoAgent
from Utils.AsyncConfig import async_config
from Utils.logs.LoggingConfig import logger


class AsyncImproveInfoAgent:
    """异步ImproveInfoAgent包装器，避免LLM调用阻塞其他接口"""

    def __init__(self):
        self._improve_agent = ImproveInfoAgent()

    async def evaluate_improve_info_async(self, evaluate: str):
        """异步版本的完善信息评估"""
        try:
            return await self._improve_agent.evaluateImproveInfo(
                evaluate
            )
        except Exception as e:
            logger.error(f"Error in evaluate_improve_info_async: {str(e)}")
            raise e
