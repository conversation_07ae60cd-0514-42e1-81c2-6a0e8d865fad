import re

from langchain.chains.qa_with_sources.map_reduce_prompt import question_prompt_template
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import RunnableLambda
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.InterviewQuestions import InterviewQuestions, Questions, InterviewQuestionsAnswer, \
    InterviewQuestionsGroup
from Models.agent.TalentInfoProcessing import ContentInfo
from Utils.BaiduSearchUtil import BaiduSearchUtil
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger

input_sys_template = SystemMessagePromptTemplate.from_template(
    """
    你是一位经验丰富的人力资源管理专家，精通人才选拔与评估，对不同岗位的技能要求和素质标准有着深入的理解，擅长设计针对性强、覆盖面广的面试问题。
    背景:
        公司在招聘过程中需要对候选人进行全面评估，以确保其符合岗位要求和公司文化，因此需要设计一套科学、有效的面试问题。
        你将根据招聘岗位信息和岗位要求，生成一系列面试问题，以帮助公司评估候选人的能力和适应能力(主要是根据招聘岗位来出题，要求用来辅助)。
    技能：
        你具备组织行为学、心理学、人才测评技术以及行业知识的综合能力，能够根据不同岗位的特点和公司的战略需求，设计出高质量的面试问题。
    约束：
        1. 面试的每个问题必须跟所属能力和招聘岗位有关系，且每个问题的难度和广度都应适当均衡。
        2. 面试问题应符合法律法规和道德标准，避免歧视性问题，确保公平性和客观性，同时应具有一定的灵活性，以适应不同候选人的回答风格。
        3. 结合面试者简历中的个人经历去生成一些与面试者有关的问题。
    注意事项:
        1. 确保输出格式中的信息一定是跟主要类型有关联的,并且一个类型中必须有且只有两个问题。
        2. 每个问题类型的输出格式都是字符数组,必须严格按照规定的格式进行输出。
        3. 输出的信息必须是有效的JSON格式，一定不能包含任何额外的字符或空格。
    输出格式：{format_instructions}
    /no_think
   """
)
input_template = HumanMessagePromptTemplate.from_template(
    "招聘岗位：{position}"
    "题目类型：{type}"
    "岗位要求：{jobDesc}"
    "个人经历：{userInfo}"
)
# 基于百度结果查询出现的面试题目
search_template = SystemMessagePromptTemplate.from_template(
    """
    你是一位经验丰富的人力资源管理专家，精通人才选拔与评估，对不同岗位的技能要求和素质标准有着深入的理解，擅长设计针对性强、覆盖面广的面试问题。
    背景:
        公司在招聘过程中需要对候选人进行全面评估，因此需要设计一套科学、有效的面试问题。
    任务:
        根据【联网搜索结果】+【岗位基础信息】生成面试题。
    约束:
        1. 每个问题必须跟岗位实际要求相关，且难度适中。
        2. 符合法律法规和道德标准，避免歧视。
        3. 每个类型必须输出两个题目。
    输出格式：{format_instructions}
    /no_think
    """
)
search_input_template = HumanMessagePromptTemplate.from_template(
    "岗位名称：{position}\n\n{search_context}"
)


question_pt_template = SystemMessagePromptTemplate.from_template(
    """
    - Role: 人力资源测评专家和命题专家
    - Background: 用户需要根据岗位名称来优化已有的题目和根据岗位名称生成新的题目，同时要求提供答案。这表明用户可能在进行招聘、培训或者人才评估等工作，需要精准且有效的题目来匹配岗位要求。
    - Profile: 你是一位在人力资源领域和命题设计方面有着丰富经验的专家，对不同岗位的技能要求、知识体系和能力模型有着深入的了解，擅长根据岗位特点设计出有针对性的测评题目。
    - Skills: 你具备岗位分析能力、测评工具设计能力、题目优化能力以及创新思维，能够结合岗位名称快速识别关键能力点，并据此设计出高质量的题目和答案。
    - Goals:
      1. 分析岗位名称，明确岗位的关键技能和能力要求。
      2. 优化已有的题目，使其更贴合岗位实际需求，提高题目效度，并提供优化后的答案。
      3. 根据岗位特点，生成新的题目，丰富题库，提升测评的全面性和准确性，并提供新题目的答案
      4. 生成的新题目必须要贴合岗位实际工作情况，避免受到用户自己出的题影响。
    - Constrains: 生成的题目应符合岗位的实际情况，避免出现与岗位无关或偏离主题的内容；题目难度应适中，能够有效区分不同水平的候选人；题目应具有一定的创新性，避免过于陈旧或常见的题目。
    - OutputFormat: 输出应包括优化后的题目列表和根据岗位名称新生成的题目列表，每个题目应包含题干、选项（如有）、考察点说明以及对应答案。
    - Workflow:
      1. 解析岗位名称，提取岗位核心要素和关键能力点。
      2. 对已有题目进行评估，根据岗位要求进行针对性的优化调整，并编写优化后的答案。
      3. 基于岗位特点和能力要求，设计新的题目，编写对应答案，确保题目覆盖岗位的关键技能和知识领域。
      4. 输出问题的数量必须控制在12道题以内，不用必须是12道题目，但是不能超过12道题。
      5. 生成的试题类型必须是简答题类型，不能输出判断、选择等题目类型。
      6. 如果联网搜索结果存有信息，则结合联网搜索的结果生成。
    输出格式：{format_instructions}
    
    /no_think
    """
)
question_input_template = HumanMessagePromptTemplate.from_template(
    "岗位名称：{position}"
    "试题列表：{question}"
    "岗位要求：{jobDesc}"
    "联网搜索结果：{search_context}"
)


class InterviewQuestionsAgent:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__output_parser = PydanticOutputParser(pydantic_object=InterviewQuestions)
        self.__output_parser_info = PydanticOutputParser(pydantic_object=Questions)
        self.__output_improve_info = PydanticOutputParser(pydantic_object=ContentInfo)
        self.__output_answer = PydanticOutputParser(pydantic_object=InterviewQuestionsGroup)

    # 根据LLM的名字设置LLM相关信息，包含对话信息
    def __set_llm(self, output_parser: PydanticOutputParser,
                  system_template: SystemMessagePromptTemplate,
                  human_template: HumanMessagePromptTemplate
                  ) -> RunnableWithMessageHistory | None:
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature)

        prompt_template = ChatPromptTemplate.from_messages([
            system_template,
            human_template
        ]).partial(format_instructions=output_parser.get_format_instructions())

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | output_parser)
        return runnable

    async def _formatting(self, position: str, jobDesc: str, userInfo: str, search: bool) -> InterviewQuestions:
        try:
            # 调用LLM进行解析
            logger.info(f"内容：{position}")
            if search:
                baidu_utils = BaiduSearchUtil()
                search_context = baidu_utils.build_prompt_from_search(position, jobDesc)
                talent_info = self.__set_llm(self.__output_parser, search_template, search_input_template).invoke(
                    {"position": position, "search_context": search_context}
                )
            else:
                talent_info = self.__set_llm(self.__output_parser, input_sys_template, input_template).invoke(
                    {"position": position, "type": "", "jobDesc": jobDesc, "userInfo": userInfo}
                )
            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e

    async def _formatting_info(self, position: str, type: str, jobDesc: str, userInfo: str,
                               search: bool) -> Questions:
        try:
            # 调用LLM进行解析
            logger.info(f"内容：{position}, {type}")
            if search:
                baidu_utils = BaiduSearchUtil()
                search_context = baidu_utils.build_prompt_from_search(position, jobDesc)
                talent_info = self.__set_llm(self.__output_parser_info, search_template, search_input_template).invoke(
                    {"position": position, "search_context": search_context}
                )
            else:
                talent_info = self.__set_llm(self.__output_parser_info, input_sys_template, input_template).invoke(
                    {"position": position, "type": type, "jobDesc": jobDesc, "userInfo": userInfo}
                )

            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e

    async def _formatting_answer(self, position: str, question: str, jobDesc, search: bool) -> InterviewQuestionsGroup:
        try:
            # 调用LLM进行解析
            logger.info(f"请求的内容：{position},题目：{question},要求：{jobDesc}")

            search_context: str = ""
            if search:
                baidu_utils = BaiduSearchUtil()
                search_context = baidu_utils.build_prompt_from_search(position, jobDesc)
                logger.info(f"联网查询的内容:{search_context}")
            talent_info = (self.__set_llm(self.__output_answer, question_pt_template, question_input_template)
                           .invoke({"position": position, "question": question, "jobDesc": jobDesc,"search_context":search_context}))
            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e

    def clean_resume_text(self, text):
        # 删除连续的空格，只保留一个空格
        text = re.sub(r' +', ' ', text)
        # 删除连续的换行符，只保留一个换行
        text = re.sub(r'\n+', '\n', text)
        # 删除行首和行尾的空格
        text = '\n'.join([line.strip() for line in text.split('\n')])
        # 删除空行
        text = '\n'.join([line for line in text.split('\n') if line.strip() != ''])
        return text
