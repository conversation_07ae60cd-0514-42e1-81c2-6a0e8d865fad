import os
from tokenize import group

from fastapi import APIRouter, UploadFile, File
from pydantic import BaseModel
from starlette.responses import J<PERSON>NResponse

from Configs.Config import SysConfig
from Models.AjaxResult import AjaxResult
from Models.peewee.OrmModel import TaskState, KBAnalysisTask
from Services import TodoService
from Services.SqlServer.KBAnalysisGroupServer import KBAnalysisGroupServer

from Services.SqlServer.KBAnalysisTaskServer import KBAnalysisTaskServer
from Utils.MinIOClient import MinIOClient

router = APIRouter(prefix="/agentService/api/analysisTask", tags=["AnalysisTask"])


class CreateTaskData(BaseModel):
    user_id: str
    file_url: str
    file_name: str
    file_size: int


class UpdateTaskData(BaseModel):
    group_id: str
    title: str


@router.post("/createTask")
def create_task(request: list[CreateTaskData]):
    count = 0
    for item in request:
        try:
            KBAnalysisTaskServer.insert_one_task(user_id=item.user_id, file_url=item.file_url, file_name=item.file_name,
                                                 file_size=item.file_size)
            count += 1
        except Exception:
            pass
    if count > 0:
        return AjaxResult.success(count)
    return AjaxResult.error("创建失败")


@router.get("/list")
def get_list(user_id: str, title: str = None, state: str = "ALL", order_field="update_time", desc=True,
                   page: int = 1, page_size: int = 10):
    # order_field  值  create_time , update_time
    if order_field == "create_time":
        order_field = KBAnalysisTask.create_time
    elif order_field == "update_time":
        order_field = KBAnalysisTask.update_time

    if state == "ALL":
        state = None
    elif state == "WAITING":
        state = TaskState.WAITING
    elif state == "PROCESSING":
        state = TaskState.PROCESSING
    elif state == "COMPLETED":
        state = TaskState.COMPLETED
    elif state == "FAILED":
        state = TaskState.FAILED
    elif state == "CANCELED":
        state = TaskState.CANCELED
    else:
        state = None

    result, total = KBAnalysisTaskServer.query_web_by_user_id(user_id=user_id,state=state, title_filter=title, page=page,
                                                              page_size=page_size, order_field=order_field, desc=desc)
    return AjaxResult.success_for_rows(total, result)


@router.get("/taskDetail")
def task_detail(task_id: str):
    res = KBAnalysisTaskServer.query_web_task_detail(task_id)
    TodoService.remove_contract_todo_task(str(task_id))
    return AjaxResult.success(res)


@router.get("/retry")
def task_retry(task_id: int):
    KBAnalysisTaskServer.update_state(work_id=task_id, state=TaskState.WAITING)
    return AjaxResult.success(1)


@router.get("/delete")
def task_delete(group_id: int):
    res = KBAnalysisTaskServer.delete_by_group_id(group_id)
    return AjaxResult.success(res)


@router.post("/edit")
def group_edit(request: UpdateTaskData):
    request_dict = request.model_dump()
    group_id = request_dict.pop("group_id")
    result = KBAnalysisGroupServer.update_by_id(group_id=group_id, data=request_dict)
    if result == 0:
        return AjaxResult.error("修改失败")
    return AjaxResult.success(result)


@router.get("/queryTask")
def get_task(task_id: int):
    try:
        result = KBAnalysisTaskServer.get_by_id(task_id)
        TodoService.remove_contract_todo_task(str(result.group_id))
        return AjaxResult.success(result.cus_model_to_dict())
    except Exception:
        return JSONResponse(
            status_code=404,
            content=AjaxResult.error("指定任务不存在")
        )


@router.post("/uploadFile")
async def upload_file(file: UploadFile = File(...)):
    """接收并保存上传文件"""
    try:
        # 从配置获取临时目录
        temp_dir = SysConfig["vector_gen"]["temp_dir"]

        # 创建目录（如果不存在）
        os.makedirs(temp_dir, exist_ok=True)

        # 生成保存路径
        save_path = os.path.join(temp_dir, file.filename)

        # 保存文件
        with open(save_path, "wb") as buffer:
            buffer.write(await file.read())

        file_url = MinIOClient().upload_file(save_path)

        result = KBAnalysisTaskServer.insert_one_task(user_id="123", file_url=file_url, file_name=file.filename,
                                                      file_size=file.size)

        return AjaxResult.success(result.cus_model_to_dict())
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content=AjaxResult.error(f"文件上传失败: {str(e)}").dict()
        )
