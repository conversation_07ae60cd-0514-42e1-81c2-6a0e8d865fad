from typing import Optional

from fastapi import APIRouter, Query
from openai import BaseModel

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Agents.InterviewQuestionsAgent import InterviewQuestionsAgent
from Models.AjaxResult import AjaxR<PERSON>ult
from Models.agent.TalentInfoProcessing import ContentInfo
from Utils.logs.LoggingConfig import logger

# 添加项目根目录到系统路径
router = APIRouter(prefix="/agentService/api/interview", tags=["talent"])


class QuestionsInfo(BaseModel):
    # 招聘岗位
    position: str = ""
    question: str = ""
    jobDesc: str = ""
    userInfo: str = ""
    search: bool = False


# 面试内容
class interviewContent(BaseModel):
    # 内容
    content: str = ""
    question: str = ""
    interviewRounds: str = ""


@router.post("/list")
async def chat(info: QuestionsInfo):
    try:
        if not info:
            raise AjaxResult.error()

        agent = InterviewQuestionsAgent()
        body = await agent._formatting(info.position, info.jobDesc, info.userInfo, info.search)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(message="生成试题信息异常")


@router.post("/info")
async def chat(info: QuestionsInfo):
    try:
        if not info:
            raise AjaxResult.error()

        agent = InterviewQuestionsAgent()
        body = await agent._formatting_info(info.position, info.question, info.jobDesc,
                                            info.userInfo, info.search)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(message="生成试题信息异常")


@router.post("/questionAndAnswer")
async def question(info: QuestionsInfo):
    try:
        if info is None:
            raise AjaxResult.error("参数不能为空")
        agent = InterviewQuestionsAgent()
        body = await agent._formatting_answer(info.position, info.question, info.jobDesc, info.search)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(message="完善信息失败")


@router.post("/improveInfo")
async def improveInfo(info: interviewContent):
    try:
        if info is None:
            raise AjaxResult.error("参数不能为空")
        agent = ImproveInfoAgent()
        body = await agent.hireImproveInfo(info.position, info.content, info.interviewRounds)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(message="完善信息失败")


# 生成开场白
@router.get("/introduction")
async def introduction(content: Optional[str] = Query("", title="内容"),
                       position: Optional[str] = Query("", title="岗位")):
    try:
        agent = ImproveInfoAgent()
        logger.info(f"content: {content}, position: {position}")

        body = await agent.generateIntroduction(content, position)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(message="完善信息失败")


# 通用一键完善
@router.post("/commonImproveInfo")
async def commonImproveInfo(info: ContentInfo):
    try:
        agent = ImproveInfoAgent()
        logger.info(f"content: {info.content}")
        body = await agent.commonImproveInfo(info.content)
        return AjaxResult.success(body)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(message="完善信息失败")
