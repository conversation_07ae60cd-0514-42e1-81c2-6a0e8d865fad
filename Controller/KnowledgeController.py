from anyio import fail_after
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

import random
from Agents.KBAgent import KBAgent
from Agents.OnlyChatAgent import OnlyChatAgent
from Controller.Http.HttpChatRequest import HttpChatRequest
from LLM.BaseLLMHelper import LLMModelChatMode
from Models.AjaxResult import AjaxResult
from Models.pojo.KBConversation import KBConversation
from Services.KBTrunkVectorService.TrunkVectorService import TrunkVectorRetrivalService
from Services.SqlServer.KBConversationService import KBConversationService
from Services.SqlServer.KBFileInfoService import KBFileInfoService
from Utils.KBAgentPool import kb_agent_pool
from Utils.KBInfoUtils import getKbIds

router = APIRouter(prefix="/agentService/api/knowledge", tags=["knowledge"])
# 定义异常消息提示数组
error_messages = [
    "请求处理超时了，可能是网络或服务器繁忙。请稍后再试，或者简化问题后重新发送。",
    "哎呀，响应超时了！请重试一次吧~",
    "⌛ 等得花儿都谢了… 可能是信号跑去度假啦！戳我一下再试试？",
    "⌛ 超时啦，点击重试或许能快马加鞭！",
    "🤔 服务器有点慢，再等等吧！",
    "⏳ 服务器有点忙，等会再试一次吧！",
    "😴 服务器有点累，等会再试一次吧！",
]

# 获取服务实例
def get_service():
    return KBConversationService()


def get_file_service():
    return KBFileInfoService()


@router.post("/chat")
async def chat(request: HttpChatRequest, service: KBConversationService = Depends(get_service),
               file_service: KBFileInfoService = Depends(get_file_service)):
    try:
        # 查询知识库Id
        if request.open_kb_search:
            kb_ids = getKbIds(request.group_id)
        else:
            kb_ids = None
        with fail_after(50):  # 10秒超时
            agent = kb_agent_pool.try_get_agent(
                request.conversation_id,
                kb_ids,
                lambda conversation_id: getAgent(request, kb_ids)
            )
            if request.model_id is not None:
                # 增加默认值
                if request.model_think is None:
                    request.model_think = 0
                chat_mode=LLMModelChatMode(request.model_think)
                agent.switch_llm(request.model_id, chat_mode)
            if kb_ids is not None:
                request.file_ids = file_service.selectFileIdsByKbIds(kb_ids)
            if isinstance(agent, KBAgent):
                agent.load_kb_index(request.file_ids)
            # 更新列表对话时间
            params = KBConversation(id=request.conversation_id, model_id=request.model_id)
            service.update(params)
            return await agent.stream_response(request.question, request.internet_search)
    except TimeoutError:
        random_message = error_messages[random.randint(0, len(error_messages) - 1)]
        if agent is not None:
            try:
                await agent._saveConversationMemory(request.question, random_message)
            except Exception as e:
                random_message = random_message+"(本次问答无法保留在问答历史记录中库😢🙏)"
                print(f"Error in stream_chat: {str(e)}")
        return AjaxResult.error(random_message)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


class HttpChatStopRequest(BaseModel):
    conversation_id: str  # 会话id

@router.post("/stop_stream")
def stop_stream(request: HttpChatStopRequest):
    try:
        agent = kb_agent_pool.get_agent(request.conversation_id)
        if agent:
            agent.stop_stream()

        return AjaxResult.success("关闭完成")
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


class HttpKnowledgeBaseRequest(BaseModel):
    question: str
    kb_ids: list[int]


@router.post("/kb")
def kb(request: HttpKnowledgeBaseRequest):
    try:
        result = TrunkVectorRetrivalService.hybrid_search(query=request.question, kb_ids=request.kb_ids)

        return AjaxResult.success(result)
    except Exception as e:
        print(f"Error in stream_chat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


def getAgent(request: HttpChatRequest,kb_ids:str):
    if (request.file_ids is None or len(request.file_ids) == 0) and kb_ids is None:
        return OnlyChatAgent(request.conversation_id, request.group_id)
    else:
        return KBAgent(request.conversation_id, request.group_id)
