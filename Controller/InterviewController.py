from fastapi import APIRouter, UploadFile, File
from pydantic import BaseModel
from typing import List, Optional

from Agents.InterviewAgent import InterviewAgent
from Models.AjaxResult import AjaxResult
from Utils.logs.LoggingConfig import logger

router = APIRouter(prefix="/agentService/api/interview", tags=["interview"])

class GenerateEvaluation(BaseModel):
    text: str

@router.post("/generateEvaluation")
def generate_evaluation(request: GenerateEvaluation):
    res=InterviewAgent.generate_evaluation(request.text)
    return AjaxResult.success(res)


class InterviewQuestion(BaseModel):
    """面试题目信息(与Java侧 TbInterviewJobPaperQuestion 对齐)"""
    question: str
    answer: Optional[str] = None
    remark: Optional[str] = None

class GenerateEvaluationInInterview(BaseModel):
    """Java 侧 generateOnlineInterviewEvaluation 请求体"""
    conversationRecord: str = ""          # 面试对话记录
    interviewQuestions: Optional[List[InterviewQuestion]] = None  # 题目列表
    fullContent: Optional[str] = None      # Java 侧拼接的完整内容
    id: Optional[str] = None               # 请求 ID，可选

@router.post("/generateOnlineInterviewEvaluation")
def generate_evaluation_in_interview(request: GenerateEvaluationInInterview):
    """适配 Java generateOnlineInterviewEvaluation 调用，返回 AI 评语和评分"""
    try:
        # 如果 Java 侧已经拼接好完整内容则直接使用；否则在此构建
        if request.fullContent and request.fullContent.strip():
            content = request.fullContent
        else:
            # 1) 面试题信息
            questions_part = []
            if request.interviewQuestions:
                questions_part.append("面试试题信息：")
                for idx, q in enumerate(request.interviewQuestions, start=1):
                    questions_part.append(f"题目{idx}：{q.question}")
                    if q.answer:
                        questions_part.append(f"标准答案：{q.answer}")
                    if q.remark:
                        questions_part.append(f"备注：{q.remark}")
                    questions_part.append("")  # 空行分隔
            # 2) 面试对话记录
            questions_text = "\n".join(questions_part)
            content = f"{questions_text}\n面试对话记录：\n{request.conversationRecord}"

        if not content:
            return AjaxResult.error(message="content 为空，无法生成面试评价")

        logger.info(f"生成在线面试评价，请求 ID: {request.id}")
        logger.debug(f"内容长度: {len(content)}")

        # 调用 InterviewAgent.generate_evaluation_in_interview
        llm_res = InterviewAgent.generate_evaluation_in_interview(content, "")

        # 组装返回结果（文本 + ID）
        response_data = {
            "id": request.id,
            "text": llm_res
        }
        return AjaxResult.success(response_data)

    except Exception as e:
        logger.error(f"生成面试评价失败: {str(e)}", exc_info=True)
        return AjaxResult.error(message=f"生成面试评价失败: {str(e)}")
