from datetime import datetime

from pydantic import BaseModel, Field


# 待办任务传输类
class TodoTaskDto(BaseModel):
    kbId: int | None = Field(None, description="知识库ID")
    eventId: str | None = Field(None, description="事件ID")
    title: str | None = Field(None, description="任务标题")
    userId: int | None = Field(None, description="用户ID")
    eventTime: str | None = Field(None, description="事件时间")
    status: int | None = Field(None, description="任务状态")
