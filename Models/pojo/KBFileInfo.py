from datetime import datetime

from pydantic import BaseModel
from typing import Optional, Any


class KBFileInfo(BaseModel):
    file_id: str | None = None # 文件ID
    file_name: str | None = None # 文件名称
    file_original_name: str | None = None # 文件原始名称
    content: str | None = None # 文件内容
    file_extension: str | None = None # 文件扩展名
    allow_download: int | None = None # 是否允许下载
    file_size: int | None = None # 文件大小
    file_state: int | None = None # 文件状态 0: 待解析, 1: 解析中, 2：解析完成
    file_enable: int | None = None # 启用状态  0: 可用, 1: 不可用
    update_time: datetime | None = None # 更新时间
    upload_time: datetime | None = None # 上传时间
    trunk_count: int | None = None # 分块数量
    kb_id: int | None = None # 知识库ID
    del_flag: int | None = None # 删除标志：0-正常，1-删除,2删除失败

    def __init__(self, **data: Any):
        super().__init__(**data)
        self.upload_time = data.get('upload_time', datetime.now())
        self.update_time = data.get('update_time', datetime.now())
        self.trunk_count = data.get('trunk_count', 0)
        #self.file_enable = data.get('file_enable', 1)
        
        if self.file_name and not self.file_extension:
            if '.' in self.file_name:
                self.file_extension = self.file_name.split('.')[-1]

    def to_dict(self):
        return {
            "id": self.file_id,
            "fileName": self.file_name,
            "fileOriginalName": self.file_original_name,
            "fileUrl": self.content,
            "fileType": self.file_extension,
            "isDownload": "1" if self.allow_download == 1 else "0",
            "fileSize": self.file_size,
            "status": self.file_state,
            "isAvailable": "1" if self.file_enable == 1 else "0",
            "uploadTime": self.upload_time.strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S"),
            "segmentCount": self.trunk_count,
            "kbId": self.kb_id
        }

    def to_dict_vo(self):
        return {
            "id": self.file_id,
            "fileName": self.file_name,
            "fileOriginalName": self.file_original_name,
            "fileType": self.file_extension,
            "isDownload": "1" if self.allow_download == 1 else "0",
            "fileSize": self.file_size,
            "status": self.file_state,
            "isAvailable": "1" if self.file_enable == 1 else "0",
            "uploadTime": self.upload_time.strftime("%Y-%m-%d %H:%M:%S"),
            "segmentCount": self.trunk_count,
        }

    def to_download(self):
        return {
            "fileName": self.file_name,
            "fileOriginalName": self.file_original_name,
            "fileUrl": self.content
        }

    def __iter__(self):
       yield self.file_id
       yield self.file_name
       yield self.file_original_name
       yield self.content
       yield self.file_extension
       yield self.allow_download
       yield self.file_size
       yield self.file_state
       yield self.file_enable
       yield self.update_time
       yield self.upload_time
       yield self.kb_id
       yield self.trunk_count
    class Config:
        from_attributes = True
        extra = 'ignore'
        json_schema_extra = {
            "comment": "知识库文件信息表"
        }