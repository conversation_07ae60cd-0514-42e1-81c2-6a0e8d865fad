from typing import Optional, List

from pydantic import BaseModel, Field


class Questions(BaseModel):
    question1: str = Field(description="问题1", default=None)
    question2: str = Field(description="问题2", default=None)

class InterviewQuestions(BaseModel):
    express: Optional[List[str]] = Field(description="沟通表达能力", default=None)
    professional: Optional[List[str]] = Field(description="专业能力", default=None)
    learningGrowth: Optional[List[str]] = Field(description="学习与成长能力", default=None)
    resilience: Optional[List[str]] = Field(description="抗压能力", default=None)
    stability: Optional[List[str]] = Field(description="稳定性与个人规划", default=None)
    problemSolving: Optional[List[str]] = Field(description="问题解决能力", default=None)

# 试题
class InterviewQuestionsAnswer(BaseModel):
    question: str = Field(description="问题", default=None)
    answer: str = Field(description="答案", default=None)


# 试题组
class InterviewQuestionsGroup(BaseModel):
    questions: List[InterviewQuestionsAnswer] = Field(description="面试试题生成", default=None)
