from typing import List, Optional

from pydantic import BaseModel, Field
import re
from Models.agent.ResumeInfo import WorkExperience, EducationInfo, ProjectExperience
from Utils.StringUtils import StringUtils


# 分块内容
class ContentTrunk(BaseModel):
    basicInfo: Optional[str] = Field(description="基本信息", default=None)
    workExperience: Optional[str] = Field(description="工作经历", default=None)
    educationExperience: Optional[str] = Field(description="教育经历", default=None)
    projectExperience: Optional[str] = Field(description="项目经历", default=None)


# 基本信息
class TalentAgentProcessing(BaseModel):
    def __init__(self, **data):
        super().__init__(**data)
        self._convert(**data)

    userName: Optional[str] = Field(description="姓名", default=None)
    sex: Optional[str] = Field(description="性别", default="2")
    age: Optional[int] = Field(description="年龄", default=None)
    email: Optional[str] = Field(description="邮箱", default=None)
    ethnicity: Optional[str] = Field(description="民族", default=None)
    marriageStatus: Optional[str] = Field(description="婚姻状态", default=None)
    workStatus: Optional[str] = Field(description="工作状态（在职或离职）", default=None)
    phone: Optional[str] = Field(description="手机号码", default=None)
    avatar: Optional[str] = Field(description="照片地址", default=None)
    currentAddress: Optional[str] = Field(description="现居地址", default=None)
    politicalStatus: Optional[str] = Field(description="政治面貌", default=None)
    introduction: Optional[str] = Field(description="个人简介(从内容中提取一些个人相关的信息并即简明扼要的处理一下)", default=None)
    jobIntent: Optional[str] = Field(description="应聘职位", default=None)
    salaryExpectation: Optional[str] = Field(description="期望薪资", default=None)
    yearsOfExperience: Optional[int] = Field(description="工作经验（工作年限）", default=None)
    workOfExperience: Optional[str] = Field(description="工作经验（工作年限）", default=None)
    salaryRangeLowerBound: Optional[int] = Field(description="期望薪资下限", default=None)
    salaryRangeUpperBound: Optional[int] = Field(description="期望薪资上限", default=None)

    def _convert(self, **data):
        if  StringUtils.is_not_empty(self.workOfExperience):
            # 判断是否为纯数字
            if self.workOfExperience.isdigit():
                self.yearsOfExperience = int(self.workOfExperience)
            elif self.workOfExperience.endswith("年"):
                self.yearsOfExperience = int(self.workOfExperience.replace("年", ""))
            elif re.search("年", self.workOfExperience):
                self.yearsOfExperience = int(self.workOfExperience.split("年")[0])


# 教育信息与工作经历
class Undergo(BaseModel):
    education: Optional[str] = Field(description="最高学历")
    major: Optional[str] = Field(description="专业（教育信息中最高的专业名称）", default=None)
    schoolName: Optional[str] = Field(description="毕业学校（教育信息中最高学历的院校名称）", default=None)
    foreignProficiency: Optional[str] = Field(description="外语水平", default=None)
    professionalLevel: Optional[str] = Field(description="专业水平", default=None)
    certificate: Optional[str] = Field(description="个人证书或资质(使用 ',' 拼接)", default=None)
    tbEducationInfoList: Optional[List[EducationInfo]] = Field(description="教育经历列表", default=None)
    tbWorkExperienceList: Optional[List[WorkExperience]] = Field(description="工作经历列表(入职过的公司)", default=None)

# 项目经验
class ProjectExperienceAgentProcessing(BaseModel):
    skillList: List[Optional[str]] = Field(description="技能列表", default=None)
    tbProjectExperienceList: Optional[List[ProjectExperience]] = Field(description="项目经历列表")


# 评分对象
class TalentScore(BaseModel):
    jobHoppingRateScore: Optional[int] = Field(description="工作经历得分", default=None)
    jobHoppingRateEvaluate: Optional[str] = Field(description="工作经历评价", default=None)
    minimumEducationScore: Optional[int] = Field(description="教育经历得分", default=None)
    educationEvaluate: Optional[str] = Field(description="教育经历评价", default=None)
    salaryRangeScore: Optional[int] = Field(description="期望薪资得分", default=None)
    salaryRangeEvaluate: Optional[str] = Field(description="期望薪资评价", default=None)
    workExperienceScore: Optional[int] = Field(description="工作经验得分", default=None)
    workExperienceEvaluate: Optional[str] = Field(description="工作经验评价", default=None)
    totalScore: Optional[float] = Field(description="总分(保留小数点后两位)", default=None)
    totalEvaluate: Optional[str] = Field(description="总体评价", default=None)
    matchScore: Optional[float] = Field(description="匹配分", default=None)

# 一键完善
class ContentInfo(BaseModel):
    content: str = Field(description="完善后的内容", default=None)
