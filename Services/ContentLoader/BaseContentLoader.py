import uuid
from abc import ABC, abstractmethod
from typing import List
import re

from langchain.document_loaders.base import BaseLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from langchain_experimental.text_splitter import SemanticChunker

from LLM.LLMManager import embeddings_manager


class BaseContentLoader(ABC):
    def __init__(self, content: str,chunk_size:int=400):
        self._content = content
        self.chunk_size = chunk_size
        self.recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=int(chunk_size/10),
            separators=["\n", "。", "！", "？", ".", "!", "?", "；", ";", "，", ","],
            length_function=len,
            keep_separator=True # 保留符号 保证语义的完整性
        )
        # 语义分割器
        self.semantic_splitter = SemanticChunker(embeddings=embeddings_manager.get_embeddings(),
                                             min_chunk_size=int(chunk_size/2),
                                             sentence_split_regex=r"(?<=[。？！.?!])")

    def _is_use_common_splitter(self) -> bool:
        return True

    def load(self, file_name: str) -> List[Document]:
        loader = self._get_loader()

        docs: List[Document] = []

        # 一下加载完即可无需分页加载。
        # TODO: 文件内容解析的不准确（内容缺失不全）
        for document in loader.load():

            if not document.page_content:
                continue

            # 移除所有的空格和\t，将多个换行符处理成一个换行符
            cleaned_text = re.sub(r'[ \t]+', ' ', re.sub(r'\n+', '\n', document.page_content)).strip()
            document.page_content = cleaned_text
            document.metadata = {
                "source": file_name,
            }

            docs.append(document)

        if self._is_use_common_splitter():
            return self.__set_docs_ids(self._split_documents(docs))
        else:
            return self.__set_docs_ids(docs)

    @staticmethod
    def __set_docs_ids(docs: List[Document]) -> List[Document]:
        for doc in docs:
            doc.id = str(uuid.uuid4())
        return docs

    @abstractmethod
    def _get_loader(self) -> BaseLoader:
        pass

    def _split_documents(self, docs: List[Document]) -> List[Document]:
        # 文本分割器
        # split_docs = self.splitter.split_documents(docs)
        # 语义分割器
        # 第一轮语义分割
        max_semantic_size = self.chunk_size * 2
        min_chunk_size = self.chunk_size / 2

        semantic_chunks = self.semantic_splitter.split_documents(docs)
        # 改进后的块处理逻辑
        temp_chunks = []
        for chunk in semantic_chunks:
            if len(chunk.page_content) > max_semantic_size:
                sub_chunks = self.recursive_splitter.split_documents([chunk])
                temp_chunks.extend(sub_chunks)
            else:
                temp_chunks.append(chunk)

        # 改进的合并算法（保持顺序）
        final_chunks = []
        buffer_chunk = None
        for chunk in temp_chunks:
            current_content = chunk.page_content

            if buffer_chunk:
                current_content = buffer_chunk.page_content + " " + current_content
                buffer_chunk = None

            if len(current_content) < min_chunk_size:
                if final_chunks:
                    # 合并到前一个块并保留元数据
                    final_chunks[-1].page_content += " " + current_content
                else:
                    # 首个块过小则暂存
                    buffer_chunk = Document(page_content=current_content, metadata=chunk.metadata)
            else:
                final_chunks.append(Document(page_content=current_content, metadata=chunk.metadata))

        # 处理最后的缓冲块
        if buffer_chunk:
            if final_chunks:
                final_chunks[-1].page_content += " " + buffer_chunk.page_content
            else:
                final_chunks.append(buffer_chunk)

        return final_chunks