import requests

from Configs.Config import SysConfig
from Models.dto.TodoInfoDto import TodoTaskDto
from Utils.logs.LoggingConfig import logger

url = SysConfig["talent"]["talent_url"]


def add_file_todo_task(todo: TodoTaskDto):
    """
    添加文件待办任务
    :return:
    """
    try:
        json_string = todo.model_dump_json(indent=4)
        # 发送请求
        headers = {
            'Content-Type': 'application/json',
        }
        response = requests.post(url + "/talentbase/agentTodo/fileAdd", data=json_string, headers=headers)
        if response.status_code == 200:
            logger.info("添加待办任务成功！")
            return True
        else:
            logger.error(f"添加待办任务失败: {response.text}")
            return False
    except requests.RequestException as e:
        logger.error(f"添加待办任务失败: {e}")
        raise


def remove_file_todo_task(file_id: str):
    """
    删除文件待办任务
    :return:
    """
    try:
        # 发送请求
        headers = {
            'Content-Type': 'application/json',
        }
        response = requests.delete(url + f"/talentbase/agentTodo/fileDel/{file_id}", headers=headers)
        if response.status_code == 200:
            logger.info("删除待办任务成功！")
            return True
        else:
            logger.error(f"删除待办任务失败: {response.text}")
            return False
    except requests.RequestException as e:
        logger.error(f"删除待办任务失败: {e}")
        raise

# 合同解析新增待办任务
def add_contract_todo_task(todo: TodoTaskDto):
    """
    添加合同待办任务
    :return:
    """
    try:
        json_string = todo.model_dump_json(indent=4)
        # 发送请求
        headers = {
            'Content-Type': 'application/json',
        }
        response = requests.post(url + "/talentbase/agentTodo/contractAdd", data=json_string, headers=headers)
        if response.status_code == 200:
            logger.info("添加待办任务成功！")
            return True
        else:
            logger.error(f"添加待办任务失败: {response.text}")
            return False
    except requests.RequestException as e:
        logger.error(f"添加待办任务失败: {e}")
        raise

# 合同解析删除待办
def remove_contract_todo_task(contract_id: str):
    """
    删除合同待办任务
    :return:
    """
    try:
        # 发送请求
        headers = {
            'Content-Type': 'application/json',
        }
        response = requests.delete(url + f"/talentbase/agentTodo/contractDel/{contract_id}", headers=headers)
        if response.status_code == 200:
            logger.info("删除待办任务成功！")
            return True
        else:
            logger.error(f"删除待办任务失败: {response.text}")
            return False
    except requests.RequestException as e:
        logger.error(f"删除待办任务失败: {e}")
        raise
