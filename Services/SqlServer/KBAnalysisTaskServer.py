import json
from datetime import datetime
from typing import List

from Agents.AnalysisFactory import AnalysisFactory
from Models.dto.TodoInfoDto import TodoTaskDto
from Models.peewee.OrmModel import KBAnalysisTask, TaskState, KBAnalysisGroup
from Services import TodoService
from Services.SqlServer.KBAnalysisGroupServer import KBAnalysisGroupServer


class KBAnalysisTaskServer:

    @staticmethod
    def insert(data: KBAnalysisTask):
        data.save()
        return data

    @staticmethod
    def insert_many(data: List[KBAnalysisTask]):
        return KBAnalysisTask.cus_insert_many(data)

    @staticmethod
    def insert_one_task(user_id: str, file_url: str, file_name: str, file_size: int):
        # 先创建一个组
        group = KBAnalysisGroupServer.insert(KBAnalysisGroup(user_id=user_id, title=file_name))
        KBAnalysisTaskServer.insert(
            KBAnalysisTask(group_id=group.id, file_url=file_url, file_name=file_name, file_size=file_size))
        return group

    @staticmethod
    def query_web_by_user_id(user_id: str, state: TaskState = None, title_filter: str = None, page: int = 1,
                             page_size: int = 10,
                             order_field=None, desc=True):
        # 先搜索所有的组
        groups = KBAnalysisGroupServer.query(KBAnalysisGroup.user_id == user_id)

        if groups:
            # 生成ID到标题的字典，并增加title过滤
            groups_dict = {
                group.id: group.title
                for group in groups
                if title_filter is None or title_filter.lower() in group.title.lower()
            }

            if not groups_dict:  # 如果没有匹配的组
                return [], 0

            group_ids = list(groups_dict.keys())
            # 再搜索所有的任务
            conditions = [KBAnalysisTask.group_id.in_(group_ids)]
            if state is not None:
                conditions.append(KBAnalysisTask.state_equals(state))

            tasks, total = KBAnalysisTask.cus_query_page(*conditions,
                                                         page=page,
                                                         page_size=page_size, is_dic=False, order_field=order_field,
                                                         desc=desc)
            final_result = []
            for task in tasks:
                task.result = ""
                extra = task.extra
                task_dir = task.cus_model_to_dict()
                if extra:
                    extra_js = json.loads(extra)
                    task_dir["height_risk_count"] = extra_js["high_risk_count"]
                    task_dir["medium_risk_count"] = extra_js["medium_risk_count"]
                    task_dir["low_risk_count"] = extra_js["low_risk_count"]
                    task_dir["risk_total_count"] = extra_js["risk_total_count"]
                else:
                    task_dir["height_risk_count"] = 0
                    task_dir["medium_risk_count"] = 0
                    task_dir["low_risk_count"] = 0
                    task_dir["risk_total_count"] = 0
                task_dir["title"] = groups_dict.get(task.group_id)
                task_dir.pop("result", None)
                final_result.append(task_dir)
            return final_result, total
        return [], 0

    @staticmethod
    def query_web_task_detail(task_id: str):
        # 再搜索所有的任务
        task = KBAnalysisTask.get_by_id(task_id)
        return AnalysisFactory.get_analysis_result_by_bean(task)

    @staticmethod
    def get_by_id(id: int):
        return KBAnalysisTask.get_by_id(id)

    @staticmethod
    def query(*params, page=1, page_size=10, is_dic=False):
        return KBAnalysisTask.cus_query_page(*params, page=page, page_size=page_size, is_dic=is_dic)

    @staticmethod
    def query_by_group_id(group_id: int):
        return KBAnalysisTask.cus_query(KBAnalysisTask.group_id == group_id)

    @staticmethod
    def delete_by_group_id(group_id: int):
        KBAnalysisGroupServer.delete_by_id(group_id)
        TodoService.remove_contract_todo_task(str(group_id))
        return KBAnalysisTask.delete().where(KBAnalysisTask.group_id == group_id).execute()

    @staticmethod
    def update(id: int, data: dict):
        return KBAnalysisTask.cus_dict_update_by_id(id, data)

    @staticmethod
    def update_state(work_id: int, state: TaskState):
        # 先修改任务状态，再修改组状态
        an_task = KBAnalysisTask.get_by_id(work_id)
        if state == TaskState.WAITING and an_task.state == TaskState.COMPLETED:
            an_task.state = state
            an_task.deal_chunk_count = 0
            an_task.result = None
        else:
            an_task.state = state

        # 新增待办
        stateName = None
        stateCode = None
        if state == TaskState.COMPLETED:
            stateName = "成功"
            stateCode = 0
        elif state == TaskState.FAILED:
            stateName = "失败"
            stateCode = 1
        elif state == TaskState.WAITING:
            # 等待状态，说明重置了先请求删除
            TodoService.remove_contract_todo_task(an_task.id)
            pass
        an_task.save()

        # 所有任务都未开始，组状态为未开始，所有任务都完成，组完成，其余为进行中
        group = KBAnalysisGroup.get_by_id(an_task.group_id)
        all_tasks = KBAnalysisTask.cus_query(KBAnalysisTask.group_id == an_task.group_id)

        # 统计任务状态
        waiting_count = 0
        completed_count = 0

        for task in all_tasks:
            if task.state == TaskState.WAITING:
                waiting_count += 1
            elif (task.state == TaskState.COMPLETED
                  or task.state == TaskState.FAILED
                  or task.state == TaskState.CANCELED):
                completed_count += 1

                # 更新组状态逻辑
        if waiting_count == len(all_tasks):
            group.state = TaskState.WAITING
        elif completed_count == len(all_tasks):
            group.state = TaskState.COMPLETED
        else:
            group.state = TaskState.PROCESSING

        group.save()

        if stateName is not None and stateCode is not None:
            title = f"'{an_task.file_name}'合同审查分析{stateName}"
            todo_task = TodoTaskDto(title=title, eventId=str(group.id), userId=group.user_id,
                                    eventTime=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), status=stateCode)
            TodoService.add_contract_todo_task(todo_task)

        return an_task
