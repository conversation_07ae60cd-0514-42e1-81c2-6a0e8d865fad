import requests

from Configs.Config import SysConfig
from Utils.logs.LoggingConfig import logger

url = SysConfig["talent"]["talent_url"]


# 查询kbIds 返回数组
def getKbIds(userId: str):
    # 设置请求头，指定内容类型为 JSON
    headers = {
        'Content-Type': 'application/json',
    }
    # 发送 Get 请求到 Java API
    try:
        response = requests.get(f'{url}/knowledgebase/base/getIdsByUserId/{userId}',
                                headers=headers)
        # 检查响应状态码
        if response.status_code == 200:
            response_body = response.json()
            if response_body.get("code") == 200:
                data = response.json()
                # 获取data中的数组
                map = data.get("data")
                if map is None:
                    return ""
                kb_ids = map.get("ids")
                logger.info(f"获取知识库Id成功！kb_ids={kb_ids}")
                return kb_ids
            else:
                logger.info("获取知识库Id失败！")
        else:
            logger.info(f"请求失败，状态码：{response.status_code}")
            logger.info("响应内容：", response.text)
    except requests.exceptions.RequestException as e:
        logger.error(f"获取知识库Id失败：{e}")